# 模糊测试器模型定义
# 定义了用于LLM安全测试的各种模型接口和实现
import functools
import gc
import inspect
from abc import ABC, abstractmethod

import openai
import tiktoken
import torch

def greedy_sampler(logits):
    """
    贪心采样器：选择概率最高的token

    Args:
        logits: 模型输出的logits向量

    Returns:
        int: 概率最高的token ID
    """
    return logits.argmax()


class EmbeddingModel(ABC):
    """
    嵌入模型抽象基类
    嵌入模型可以查询文本的向量表示
    """

    @abstractmethod
    def embed(self, text):
        """
        将文本转换为嵌入向量

        Args:
            text: 输入文本

        Returns:
            嵌入向量
        """
        pass


class BlackBoxModel(ABC):
    """
    黑盒模型抽象基类
    黑盒模型只能查询文本补全，无法访问内部状态
    """

    @abstractmethod
    def complete(self, text) -> str:
        """
        文本补全方法

        Args:
            text: 输入文本

        Returns:
            str: 补全后的文本
        """
        pass

    @abstractmethod
    def tokenize(self, text):
        """
        文本分词方法

        Args:
            text: 输入文本

        Returns:
            token列表
        """
        pass

    @abstractmethod
    def decode(self, tokens) -> str:
        """
        token解码方法

        Args:
            tokens: token列表

        Returns:
            str: 解码后的文本
        """
        pass


class WhiteBoxModel(BlackBoxModel):
    """
    白盒模型抽象基类
    白盒模型提供每个补全的logits，采样过程可由调用者控制
    """

    def complete(self, text, max_tokens=100, live_print=False, **kwargs) -> str:
        """
        文本补全方法（基于流式生成）

        Args:
            text: 输入文本
            max_tokens: 最大生成token数
            live_print: 是否实时打印生成过程
            **kwargs: 其他参数

        Returns:
            str: 补全后的文本
        """
        completion = None
        for _, (_, completed_tokens) in zip(range(max_tokens), self.complete_stream(text, **kwargs)):
            completion = completed_tokens

            if live_print:
                # 持续覆盖同一行但发送换行符以更新远程日志
                # 应该与jupyter notebooks兼容
                print(f"\r{repr(self.decode(tuple(completion)))}", end="", flush=True)
        return self.decode(tuple(completion))

    @abstractmethod
    def complete_stream(self, text, **kwargs):
        """
        流式文本补全方法

        Args:
            text: 输入文本
            **kwargs: 其他参数

        Yields:
            tuple: (logits, completed_tokens) 元组
        """
        pass


class TransformersModel(WhiteBoxModel):
    """
    基于Transformers库的白盒模型实现
    支持本地运行的Transformer模型
    """

    def __init__(self, model, tokenizer, device="cuda"):
        """
        初始化Transformers模型

        Args:
            model: Transformer模型实例
            tokenizer: 对应的分词器
            device: 运行设备（默认为cuda）
        """
        self.model = model
        self.tokenizer = tokenizer
        self.device = device

    @functools.lru_cache(maxsize=1000)
    def tokenize(self, text, **kwargs):
        """
        文本分词（带缓存）

        Args:
            text: 输入文本
            **kwargs: 分词器参数

        Returns:
            list: token ID列表
        """
        text = ''.join(text)
        return self.tokenizer.encode(text, **kwargs)

    @functools.lru_cache(maxsize=1000)
    def decode(self, tokens, **kwargs):
        """
        token解码（带缓存）

        Args:
            tokens: token ID列表
            **kwargs: 解码器参数

        Returns:
            str: 解码后的文本
        """
        return self.tokenizer.decode(tokens, **kwargs)

    @torch.inference_mode()
    def complete_stream(self, text, max_tokens=None, **kwargs):
        """
        流式文本补全实现

        Args:
            text: 输入文本
            max_tokens: 最大生成token数
            **kwargs: 其他参数（stop_token_ids, sampler等）

        Yields:
            tuple: (logits, completed_tokens) 元组
        """
        input_tokens = self.tokenize(text)
        completed_tokens = []
        # 获取停止token ID列表
        stop_token_ids = kwargs.get("stop_token_ids", None) or []
        stop_token_ids.append(self.tokenizer.eos_token_id)
        # 获取采样器（默认为贪心采样）
        sampler = kwargs.get("sampler", greedy_sampler)

        last_selected_token = None
        past_key_values = None

        # 生成循环
        while max_tokens is None or len(completed_tokens) < max_tokens:
            if not past_key_values:
                # 首次推理：处理完整输入
                out = self.model(torch.as_tensor([input_tokens],
                                                 device=self.device),
                                 use_cache=True)
            else:
                # 后续推理：只处理最新token
                out = self.model(torch.as_tensor([[last_selected_token]],
                                                 device=self.device),
                                 use_cache=True,
                                 past_key_values=past_key_values)

            # 只获取最后一层的logits
            logits = out.logits[0][-1]
            past_key_values = out.past_key_values

            # 生成当前状态给调用者
            response = yield logits, completed_tokens

            if response is None:
                should_continue = True
                # 不将self传递给采样函数
                selected_token = sampler(logits)
            else:
                (selected_token, should_continue) = response

            completed_tokens.append(selected_token)
            last_selected_token = selected_token

            # 检查停止条件
            if selected_token in stop_token_ids or not should_continue:
                break

        # 清理内存
        del past_key_values, out
        gc.collect()
        torch.cuda.empty_cache()


class OpenAIChatModel(BlackBoxModel):
    """
    OpenAI聊天模型实现
    通过API调用OpenAI的聊天模型
    """

    def __init__(self, model_name, prompt_template_func=None, temperature=0.9, max_tokens=100):
        """
        初始化OpenAI聊天模型

        Args:
            model_name: 模型名称
            prompt_template_func: 提示模板函数，将文本转换为聊天格式
            temperature: 采样温度
            max_tokens: 最大生成token数
        """
        self.model_name = model_name
        # 默认提示模板：将文本作为用户消息
        self.prompt_template_func = prompt_template_func or \
                                    (lambda text: [{"role": "user", "content": text}])
        self.temperature = temperature
        self.max_tokens = max_tokens
        # 获取对应模型的分词器
        self.tokenizer = tiktoken.encoding_for_model(model_name)

    def complete(self, text, **kwargs):
        """
        文本补全实现

        Args:
            text: 输入文本
            **kwargs: 其他API参数

        Returns:
            API响应的choices部分
        """
        prompt = self.prompt_template_func(text)
        chat_completion = openai.ChatCompletion. \
            create(model="gpt-3.5-turbo",
                   messages=prompt,
                   **kwargs)
        return chat_completion.choices

    def tokenize(self, text):
        """
        文本分词

        Args:
            text: 输入文本

        Returns:
            list: token ID列表
        """
        return self.tokenizer.encode(text)

    def decode(self, tokens):
        """
        token解码

        Args:
            tokens: token ID列表

        Returns:
            str: 解码后的文本
        """
        return self.tokenizer.decode(tokens)


class OpenAIEmbeddingModel(EmbeddingModel):
    """
    OpenAI嵌入模型实现
    通过API调用OpenAI的嵌入模型
    """

    def __init__(self, model_name="text-embedding-ada-002"):
        """
        初始化OpenAI嵌入模型

        Args:
            model_name: 嵌入模型名称，默认为text-embedding-ada-002
        """
        self.model_name = model_name

    def embed(self, text):
        """
        生成文本嵌入向量

        Args:
            text: 输入文本

        Returns:
            list: 嵌入向量
        """
        return openai.Embedding.create(input=text, model=self.model_name)["data"][0]["embedding"]


class SentenceTransformerEmbeddingModel(EmbeddingModel):
    """
    SentenceTransformer嵌入模型实现
    使用本地的SentenceTransformer模型
    """

    def __init__(self, model_name="all-mpnet-base-v2"):
        """
        初始化SentenceTransformer嵌入模型

        Args:
            model_name: 模型名称，默认为all-mpnet-base-v2
        """
        from sentence_transformers import SentenceTransformer
        self.model = SentenceTransformer(model_name)

    def embed(self, text):
        """
        生成文本嵌入向量

        Args:
            text: 输入文本

        Returns:
            numpy.ndarray: 嵌入向量
        """
        return self.model.encode(text)