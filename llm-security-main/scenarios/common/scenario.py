# 场景测试框架
# 定义了用于LLM安全测试的场景基类和ChatML应用场景
from abc import ABC

from rich.prompt import Prompt
from rich.console import Console

from scenarios.common.chat_app import ChatMLApp


class Scenario:
    """
    场景测试基类
    定义了所有安全测试场景的通用接口和行为
    """
    name = "Scenario"  # 场景名称
    description = "A scenario is a test."  # 场景描述
    target = "None"  # 攻击目标

    def __init__(self, console=None, interactive=False, verbose=False):
        """
        初始化场景

        Args:
            console: 控制台对象，用于输出
            interactive: 是否为交互模式
            verbose: 是否显示详细日志
        """
        self.interactive = interactive
        self.verbose = verbose
        self.console = console or Console()

    def prompt_user(self, prompt: str, default: str) -> str:
        """
        提示用户输入

        Args:
            prompt: 提示信息
            default: 默认值

        Returns:
            str: 用户输入或默认值
        """
        if self.interactive:
            return Prompt.ask(prompt, default=default)
        else:
            return default

    def print(self, message, role=None) -> None:
        """
        打印消息（带角色颜色）

        Args:
            message: 要打印的消息
            role: 角色类型（user, system, assistant, app）
        """
        colors = {
            "user": "green",      # 用户：绿色
            "system": "blue",     # 系统：蓝色
            "assistant": "red",   # 助手：红色
            "app": "red",         # 应用：红色
        }
        color = colors.get((role or "").lower(), "white")
        if role:
            self.console.print(f"[bold {color}]{role}[/bold {color}]: {message}")
        else:
            self.console.print(message)

    def log(self, message) -> None:
        """
        记录日志（仅在verbose模式下显示）

        Args:
            message: 日志消息
        """
        if self.verbose:
            self.console.log(message)

    def run(self) -> bool:
        """
        运行场景测试
        打印摘要，调用_run()，并打印结果

        Returns:
            bool: 测试是否通过
        """
        # 打印场景信息
        self.console.print(f"[bold blue]Scenario: {self.name}[/bold blue]")
        self.console.print(f"[bold blue]Description: {self.description}[/bold blue]")
        self.console.print(f"[bold blue]Target: {self.target}[/bold blue]")
        self.console.print(f"[bold blue]Interactive: {self.interactive}[/bold blue]")

        failed = False
        try:
            self._run()
        except AssertionError as e:
            self.console.print(f"[bold red]Error: {e}[/bold red]")
            failed = True

        # 打印测试结果
        if not failed:
            self.console.print(f"[bold green]Scenario {self.name} PASS[/bold green]")
        else:
            self.console.print(f"[bold red]Scenario {self.name} FAIL[/bold red]")

        # 交互模式下等待用户输入
        while self.interactive:
            self.prompt_user("User Input: ", default="")

        return not failed

    def _run(self) -> bool:
        """
        场景具体实现（子类需要重写此方法）

        Returns:
            bool: 测试是否通过

        Raises:
            NotImplementedError: 子类未实现此方法
        """
        raise NotImplementedError("Scenario._run() not implemented.")


class ChatMLAppScenario(Scenario, ABC):
    """
    ChatML应用场景基类
    专门用于测试基于ChatML协议的聊天应用
    """
    model = "gpt-3.5-turbo"  # 默认使用的模型

    def __init__(self, tools=None, **kwargs):
        """
        初始化ChatML应用场景

        Args:
            tools: 可用的工具列表
            **kwargs: 其他参数传递给父类
        """
        super().__init__(**kwargs)
        self.app = ChatMLApp(self, model=self.model, tools=tools)

    def prompt_user(self, prompt: str, default: str) -> str:
        """
        通过ChatML应用提示用户输入

        Args:
            prompt: 提示信息
            default: 默认值

        Returns:
            str: 应用的响应
        """
        if self.interactive:
            return self.app.ask(Prompt.ask(prompt, default=default))
        else:
            return self.app.ask(default)
