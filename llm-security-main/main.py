# LLM安全攻击演示项目主程序
# 该项目演示了针对大语言模型的各种安全攻击场景
import os
import sys
import importlib
import inspect
from pathlib import Path

from scenarios.common.scenario import ChatMLAppScenario


def find_subclasses(base_class, folder_path):
    """
    查找指定文件夹中所有继承自基类的子类

    Args:
        base_class: 要查找的基类
        folder_path: 搜索的文件夹路径

    Returns:
        list: 找到的子类列表
    """
    subclasses = []
    # 将文件夹路径添加到系统路径中，以便导入模块
    sys.path.insert(0, folder_path)

    # 遍历文件夹中的所有文件
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith(".py"):
                # 构建模块路径
                module_path = Path(root) / file
                relative_module_path = module_path.relative_to(folder_path).with_suffix("")
                module_name = str(relative_module_path).replace("/", ".").replace("\\", ".")

                # 跳过特定的模块（初始化文件、通用模块、代码补全、gpt3langchain、谜题模块）
                if any(name in module_name for name in ["__init__", "common", "code-completion", "gpt3langchain", "puzzle"]):
                    continue

                try:
                    # 动态导入模块
                    module = importlib.import_module(module_name)
                except ImportError as e:
                    print(f"Error importing module {module_name}: {e}")
                    continue

                # 检查模块中的所有类
                for _, cls in inspect.getmembers(module, inspect.isclass):
                    # 如果是基类的子类且不是基类本身，则添加到列表中
                    if issubclass(cls, base_class) and cls != base_class:
                        subclasses.append(cls)

    # 移除添加的路径
    sys.path.pop(0)
    return subclasses


def menu():
    """
    显示交互式菜单，允许用户选择要运行的攻击场景
    """
    # 查找所有可用的攻击场景
    scenarios = find_subclasses(ChatMLAppScenario, os.path.dirname(__file__))

    while True:
        print("Which scenario would you like to run?")  # 您想运行哪个场景？
        print("0. Run all scenarios")  # 0. 运行所有场景
        # 显示所有可用的场景
        for i, scenario in enumerate(scenarios):
            print(f"{i + 1}. {scenario.name}")
        print("q. Quit")  # q. 退出
        choice = input("> ")

        if choice == "q":
            break
        elif choice == "0":
            # 运行所有场景（非交互模式）
            results = []
            for scenario in scenarios:
                results.append(scenario(interactive=False).run())
            # 显示所有场景的运行结果
            for scenario, result in zip(scenarios, results):
                print(f"{scenario.name} {'PASS' if result else 'FAIL'}")
        else:
            try:
                # 运行选定的场景（交互模式）
                scenario = scenarios[int(choice) - 1]
            except IndexError:
                print("Invalid choice")  # 无效选择
            else:
                scenario(interactive=True, verbose=True).run()


if __name__ == "__main__":
    menu()
